/* 基础容器 - 添加入场动画和通用过渡 */
.starlight-aside {
    padding: 1rem;
    border-inline-start: 0.25rem solid var(--sl-color-asides-border);
    color: var(--sl-color-white);
    transition: 
      transform 0.3s ease,
      box-shadow 0.3s ease,
      border-inline-start-width 0.3s ease; /* 边框宽度过渡 */
    animation: fadeInLeft 0.5s ease-out;     /* 入场动画 */
    will-change: transform, opacity;         /* 性能优化 */
  }
  
  /* 悬停效果：轻微放大 + 阴影 + 边框扩展 */
  .starlight-aside:hover {
    box-shadow: 0 6px 6px rgba(0, 0, 0, 0.149);
  }

  
  /* 入场动画：从左淡入 */
  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* 内容展开动画 */
  .starlight-aside__content {
    overflow: hidden;
    transition: 
      max-height 0.5s ease-out,
      opacity 0.3s ease;
  }
  /* 用于动态展开内容（需配合 JS 使用） */
  .starlight-aside__content.collapsed {
    max-height: 0 !important;
    opacity: 0;
  }
  
  /* 无障碍优化 */
  @media (prefers-reduced-motion: reduce) {
    .starlight-aside,
    .starlight-aside__icon {
      animation: none !important;
      transition: none !important;
    }
  }