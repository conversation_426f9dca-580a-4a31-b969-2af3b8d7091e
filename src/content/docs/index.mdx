---
title: 护城河
description: Build your moat
template: splash
hero:
  tagline: 构建护城河<hr />聚焦、持续
  image:
   light: ../../assets/moatkon.svg
   dark: ../../assets/moatkon.svg
  actions:
    - text: 联系我
      link: /contact
      icon: right-arrow
      variant: secondary
    # - text: App下载
    #   link: https://median.co/share/rxkroy#androidphone
    #   icon: seti:argdown
    #   variant: secondary
lastUpdated: 2025-04-24 00:11:25

---



import { CardGrid, LinkCard,Steps,Card,Icon,Badge} from '@astrojs/starlight/components';


<CardGrid>
	<LinkCard
		title="软件工程师"
		description="优雅且高效地处理数据"
		href="/software-engineer/readme"
	/>
	<LinkCard
		title="英语"
		description="最好的语言不是Java、C++之类的,而是英语"
		href="/english"
	/>
	<LinkCard
		title="钢琴🎹"
		description="因为我熟悉键盘,所以我选择了带按键的乐器"
		href="/music/piano/study"
	/>
	<LinkCard
		title="分享"
		description="效率工具、博主、电影🎬、视频、网站、音乐🎵"
		href="/share"
	/>
	<LinkCard
		title="AI"
		description="Artificial intelligence,智能或者说智慧,我理解的就是概率"
		href="/ai"
	/>
	<LinkCard
		title="增长计划"
		description="Growth Plan"
		href="/growth"
	/>
</CardGrid>


<Card title="“Does this help growth or not?”" icon="sun">
“这是否有助于生长？”
> 源: [What I Learned Working For Mark Zuckerberg](/english/internet/what-i-learned-working-for-mark-zuckerberg)

可以经常问问自己这段时间做的事情,是否有利于增长,至于增长的是什么?可以是余额、技能、睡眠时间等等。确保自己所做的事情是保持正向收益的

</Card>

{/* 
#### 输出 · Output

<Steps>

1. Moatkon <Badge text='专注'  variant='default' /><Badge text='聚焦'  variant='default' /><Badge text='持续'  variant='default' />
	> 当前站点即 *moatkon.com*

	沉淀

2. upupor开源项目 <Badge text='SEO友好'  variant='default' />
	<div style="color:grey"><span style="font-weight:bold">声明:</span> upupor.com 域名已售出，现与 upupor 开源项目不再有任何关联</div>

	[开源地址](https://github.com/yangrunkang/upupor)

	使用更加合理的方式处理内容

	<details>
		<summary>upupor有偿服务</summary>
		1. 代码讲解,可以回答项目中所有的技术点及设计思路 <span style="color:#0074aa;font-weight:bold">30元/次</span>
		2. 解决相关问题 <span style="color:#0074aa;font-weight:bold">50元/次</span>
		3. 部署 <span style="color:#0074aa;font-weight:bold">300元/次</span>
		4. 基于现有upupor代码,做定制开发 <span style="color:#0074aa;font-weight:bold">[联系我](/contact)</span>
	</details>

	<details>
		<summary>upupor技术栈</summary>

		##### 后端技术
		`SpringBoot`、`Java`、`MySQL`、`Redis`、`tika`、`logback`、`flyway`、`shell`、`lombok`、`MyBatis-Plus`、`fastjson`、`guava`、`lucene`、`Python`、`thumbnailator`、
		`segment`、	`minIO`
	
		##### 构建及部署
		`Maven`、`Linux`、`Docker`、`Nginx`
	
		##### 前端
		`Bootstrap`、`JQuery`、`SemanticUI`、`thymeleaf`、`HTML`、`CSS`、`JS`、`lazysizes`、`crop`、`clipboard`、`sweetalert`、`viewerjs`、`goup`、`cherry-markdown`


		##### 三方集成
		`Google AdSense`、`Google Analytics`、`Google Search`

	</details>


3. Podcast 《程序员的生活记录》
	
	[网易平台-程序员的生活记录](https://music.163.com/#/radio/?id=341445058)
	> 2015年开始录制

</Steps>
 */}
