---
title: 变更记录
description: Change Log
template: doc
lastUpdated: 2025-05-30 19:02:00
tableOfContents: false
---

import { Badge } from '@astrojs/starlight/components';


##### 2025.06.01
1. **样式优化**: 使用CSS让markdown中的图片都居中展示，提升阅读体验。<Badge text="解决方案由AugmentCode提供" variant="tip" />
2. **内容更新**: 新增电影分享页面，添加了《战争之王》相关内容和图片资源。

##### 2025.05.31
1. **图表优化**: 更新Google搜索广告相关的drawio图表，从SVG格式升级为drawio.svg格式，提升图表质量和可维护性。
2. **样式调整**: 继续优化markdown样式，改善深色模式下的显示效果。

##### 2025.05.30
1. **构建优化**: 解决Vite构建空块警告导致的样式丢失问题，通过配置rollupOptions的manualChunks和onwarn来优化构建过程。<Badge text="解决方案由AugmentCode提供" variant="tip" />
2. **样式重构**: 删除了冗余的show-case.css样式文件，清理了astro.config.mjs中的相关引用。
3. **依赖更新**: 升级了多个核心依赖包：
   - astro: 5.7.13 → 5.8.1
   - @astrojs/markdown-remark: 6.3.1 → 6.3.2
   - @astrojs/react: 4.2.7 → 4.3.0
4. **UI调整**: 重新排列了页脚的Change Log链接位置，优化了页面布局。

##### 2025.05.28
1. **文件重构**: 重命名iPad相关文件，将`useless-ipad.md`重命名为`useless-ipad-mini-6.md`，并更新了相关配置和路径引用。
2. **资源整理**: 重新组织iPad mini 6相关图片资源，将图片从`efficiency-tools`目录移动到`just`目录，优化资源结构。

##### 2025.05.27
1. **配置优化**: 继续完善astro.config.mjs配置，优化页面路由和导航结构。

##### 2025.05.26
1. **图表大更新**: 批量更新了所有软件工程相关的drawio图表文件，包括：
   - AQS相关图表（独占锁、共享锁、状态图）
   - Java基础图表（类加载器、对象创建、JVM结构等）
   - 并发编程图表（锁升级、线程池、ThreadLocal等）
   - 数据库图表（MySQL架构、事务问题等）
   - 分布式系统图表（CAP理论、任务分片、Canal等）
2. **样式优化**: 更新了markdown样式，改善了代码显示效果。
3. **组件更新**: 优化了FootSameText组件的显示逻辑。
4. **新增功能**: 创建了change-log页面，开始维护版本变更记录。

##### 2025.05.24
1. 深色模式时,通过CSS给图片添加了网页主题色,不然看不清。因为我导出的图片都是背景透明的,黑色字体遇到深色模式,直接就看不到了,所以优化了一下

##### 之前的变更
无,因为是2025.05.24才计划做这个Change Log的,虽然可以从Git提交记录获取,但是我不想,浪费时间,珍惜当下
