---
title: Dify
description: Dify
template: doc
draft: false
tableOfContents: false
lastUpdated: 2025-03-31 11:18:33
banner:
  content: 编写中
---

:::note

如果不采用本地安装,可以使用Dify的云服务,方便,快捷

https://cloud.dify.ai/

:::




```sh title="安装"
git clone https://github.com/langgenius/dify.git
cd dify/
cd docker
cp .env.example .env
docker compose up -d
```

```sh title="访问"
 http://localhost/install 
```


#### 使用Dify来发布moatkon.com
![](/ai/dify/2.png)

#### 使用Dify接入DeepSeek
![](/ai/dify/1.png)
