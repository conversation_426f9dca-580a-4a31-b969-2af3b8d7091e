{"name": "react.dev", "version": "0.0.0", "main": "/src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.0"}, "devDependencies": {}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}